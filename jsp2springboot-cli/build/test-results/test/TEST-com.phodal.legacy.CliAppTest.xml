<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.CliAppTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-07-09T13:34:12.388Z" hostname="fdhuang.local" time="0.374">
  <properties/>
  <testcase name="testAnalyzeCommandWithOptions()" classname="com.phodal.legacy.CliAppTest" time="0.231"/>
  <testcase name="testConvertCommandWithInvalidSource()" classname="com.phodal.legacy.CliAppTest" time="0.014"/>
  <testcase name="testValidateCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.013"/>
  <testcase name="testVersionCommand()" classname="com.phodal.legacy.CliAppTest" time="0.015"/>
  <testcase name="testConvertCommandWithCustomOptions()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <testcase name="testAnalyzeCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.01"/>
  <testcase name="testAnalyzeCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <testcase name="testConvertCommandWithValidDirectories()" classname="com.phodal.legacy.CliAppTest" time="0.012"/>
  <testcase name="testHelpCommand()" classname="com.phodal.legacy.CliAppTest" time="0.033"/>
  <testcase name="testMainCommandWithoutArguments()" classname="com.phodal.legacy.CliAppTest" time="0.007"/>
  <testcase name="testValidateCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.007"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
