<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.CliAppTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-07-09T13:47:12.518Z" hostname="fdhuang.local" time="0.315">
  <properties/>
  <testcase name="testAnalyzeCommandWithOptions()" classname="com.phodal.legacy.CliAppTest" time="0.225"/>
  <testcase name="testConvertCommandWithInvalidSource()" classname="com.phodal.legacy.CliAppTest" time="0.008"/>
  <testcase name="testValidateCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.007"/>
  <testcase name="testVersionCommand()" classname="com.phodal.legacy.CliAppTest" time="0.008"/>
  <testcase name="testConvertCommandWithCustomOptions()" classname="com.phodal.legacy.CliAppTest" time="0.006"/>
  <testcase name="testAnalyzeCommandWithValidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.009"/>
  <testcase name="testAnalyzeCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.005"/>
  <testcase name="testConvertCommandWithValidDirectories()" classname="com.phodal.legacy.CliAppTest" time="0.006"/>
  <testcase name="testHelpCommand()" classname="com.phodal.legacy.CliAppTest" time="0.022"/>
  <testcase name="testMainCommandWithoutArguments()" classname="com.phodal.legacy.CliAppTest" time="0.004"/>
  <testcase name="testValidateCommandWithInvalidDirectory()" classname="com.phodal.legacy.CliAppTest" time="0.005"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
