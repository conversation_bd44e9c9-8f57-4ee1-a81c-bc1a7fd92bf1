<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.utils.FileUtilsTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-07-09T13:34:12.766Z" hostname="fdhuang.local" time="0.092">
  <properties/>
  <testcase name="testIsJavaFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testFindWebXmlFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.011"/>
  <testcase name="testBackupFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.012"/>
  <testcase name="testIsJspFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testEnsureDirectoryExists()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testWriteFileContentCreatesDirectories()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.006"/>
  <testcase name="testFindJspFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.007"/>
  <testcase name="testCountFilesByExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testReadAndWriteFileContent()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testCopyFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.006"/>
  <testcase name="testGetFileNameWithoutExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testGetFileExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindJavaFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testGetRelativePath()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindFilesByExtensions()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <system-out><![CDATA[21:34:12.773 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findWebXmlFiles in component: FileUtils
21:34:12.778 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13157390493122155413/testdir
21:34:12.778 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
21:34:12.784 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit819682981826787878/test.txt
21:34:12.785 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit819682981826787878/test.txt
21:34:12.791 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit819682981826787878/test.txt.backup.1752068052786
21:34:12.791 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit819682981826787878/test.txt.backup.1752068052786
21:34:12.792 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit819682981826787878/test.txt.backup.1752068052786
21:34:12.806 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13510331641970987350/nested/deep/file.txt
21:34:12.807 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13510331641970987350/nested/deep/file.txt
21:34:12.808 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13510331641970987350/nested/deep/file.txt
21:34:12.808 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13510331641970987350/nested/deep/file.txt
21:34:12.814 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
21:34:12.816 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.tagx, .tag, .jsp, .jspx] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit8183012877552274189/testdir
21:34:12.816 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
21:34:12.828 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit18062790320252733422/test.txt
21:34:12.828 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit18062790320252733422/test.txt
21:34:12.829 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit18062790320252733422/test.txt
21:34:12.829 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit18062790320252733422/test.txt
21:34:12.833 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/test.txt
21:34:12.834 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/test.txt
21:34:12.834 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/test.txt
21:34:12.835 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/copied.txt
21:34:12.836 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/copied.txt
21:34:12.836 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2669937132698013416/copied.txt
21:34:12.847 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
21:34:12.848 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1220052452290324388/testdir
21:34:12.848 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
21:34:12.855 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
21:34:12.856 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13390347315696649002/testdir
21:34:12.856 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
