<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.phodal.legacy.utils.FileUtilsTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-07-09T13:47:12.836Z" hostname="fdhuang.local" time="0.064">
  <properties/>
  <testcase name="testIsJavaFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindWebXmlFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.006"/>
  <testcase name="testBackupFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.009"/>
  <testcase name="testIsJspFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testEnsureDirectoryExists()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testWriteFileContentCreatesDirectories()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testFindJspFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.005"/>
  <testcase name="testCountFilesByExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testReadAndWriteFileContent()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testCopyFile()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <testcase name="testGetFileNameWithoutExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testGetFileExtension()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.001"/>
  <testcase name="testFindJavaFiles()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.003"/>
  <testcase name="testGetRelativePath()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.002"/>
  <testcase name="testFindFilesByExtensions()" classname="com.phodal.legacy.utils.FileUtilsTest" time="0.004"/>
  <system-out><![CDATA[21:47:12.840 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findWebXmlFiles in component: FileUtils
21:47:12.842 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 1 web.xml files in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit10487581170515392884/testdir
21:47:12.842 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findWebXmlFiles in component: FileUtils
21:47:12.846 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12160404193932655596/test.txt
21:47:12.847 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12160404193932655596/test.txt
21:47:12.851 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Created backup: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12160404193932655596/test.txt.backup.1752068832847
21:47:12.851 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12160404193932655596/test.txt.backup.1752068832847
21:47:12.852 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit12160404193932655596/test.txt.backup.1752068832847
21:47:12.863 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13025112752031852033/nested/deep/file.txt
21:47:12.864 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13025112752031852033/nested/deep/file.txt
21:47:12.864 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13025112752031852033/nested/deep/file.txt
21:47:12.864 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13025112752031852033/nested/deep/file.txt
21:47:12.868 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
21:47:12.870 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 3 files with extensions [.jspx, .tagx, .tag, .jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit2515085162595760996/testdir
21:47:12.870 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
21:47:12.877 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11749252377358584923/test.txt
21:47:12.878 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11749252377358584923/test.txt
21:47:12.878 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11749252377358584923/test.txt
21:47:12.878 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit11749252377358584923/test.txt
21:47:12.881 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Writing file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/test.txt
21:47:12.881 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Written file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/test.txt
21:47:12.882 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Copying to /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/copied.txt file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/test.txt
21:47:12.882 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Copied file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/copied.txt
21:47:12.883 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f4c1;<![CDATA[ Reading file: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/copied.txt
21:47:12.883 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Read file completed: /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit1961860413217983838/copied.txt
21:47:12.891 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
21:47:12.891 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.java] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit17282325541701039357/testdir
21:47:12.891 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
21:47:12.897 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ]]>&#x1f680;<![CDATA[ Starting operation: findFilesByExtensions in component: FileUtils
21:47:12.898 [Test worker] INFO com.phodal.legacy.utils.FileUtils -- Found 2 files with extensions [.jsp] in /var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/junit13951323477512055988/testdir
21:47:12.898 [Test worker] INFO com.phodal.legacy.utils.LoggingUtils -- ✅ Completed operation: findFilesByExtensions in component: FileUtils
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
